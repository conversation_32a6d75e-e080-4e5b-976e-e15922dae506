#!/bin/bash

# PropBolt - Google Cloud Platform Deployment Script
# This script deploys all three services to Google Cloud

echo "🚀 PropBolt - Google Cloud Deployment"
echo "======================================"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install Google Cloud SDK first."
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Not authenticated with Google Cloud. Please run 'gcloud auth login' first."
    exit 1
fi

# Set project
echo "🔧 Setting Google Cloud project..."
gcloud config set project gold-braid-458901-v2

# Enable required APIs
echo "🔌 Enabling required APIs..."
gcloud services enable appengine.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable cloudbuild.googleapis.com

# Deploy API Server
echo "📦 Deploying API Server (api.propbolt.com)..."
cd api
gcloud app deploy app.yaml --quiet --promote
if [ $? -ne 0 ]; then
    echo "❌ API deployment failed"
    exit 1
fi
cd ..

echo "✅ API deployed successfully"



# Deploy Landing Page
echo "🏠 Deploying Landing Page (propbolt.com)..."
cd landing
gcloud app deploy app.yaml --quiet --promote
if [ $? -ne 0 ]; then
    echo "❌ Landing page deployment failed"
    exit 1
fi
cd ..

echo "✅ Landing page deployed successfully"

# Deploy Dashboard
echo "📊 Deploying Dashboard (go.propbolt.com)..."
cd dashboard
gcloud app deploy app.yaml --quiet --promote
if [ $? -ne 0 ]; then
    echo "❌ Dashboard deployment failed"
    exit 1
fi
cd ..

echo "✅ Dashboard deployed successfully"

# Deploy dispatch configuration
echo "🔀 Deploying dispatch configuration..."
gcloud app deploy dispatch.yaml --quiet
if [ $? -ne 0 ]; then
    echo "❌ Dispatch deployment failed"
    exit 1
fi

echo "✅ Dispatch configuration deployed successfully"

# Get deployment URLs
echo ""
echo "🎉 Deployment Complete!"
echo "======================="
echo "📍 Backend API: https://api-dot-gold-braid-458901-v2.uc.r.appspot.com"
echo "🏠 Landing Page: https://gold-braid-458901-v2.uc.r.appspot.com"
echo "📊 Dashboard: https://dashboard-dot-gold-braid-458901-v2.uc.r.appspot.com"
echo ""
echo "🌐 Custom Domains:"
echo "📍 api.propbolt.com → Backend API"
echo "🏠 propbolt.com → Landing Page"
echo "🏠 www.propbolt.com → Landing Page (redirect)"
echo "📊 go.propbolt.com → Dashboard"
echo ""
echo "🔧 Next steps:"
echo "1. Test all services"
echo "2. Set up Google OAuth credentials"
echo "3. Test authentication flow"
echo "4. Configure load balancing"
