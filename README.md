# PropBolt Real Estate API

Complete 13 Endpoint Implementation for `data.propbolt.com` - Python Flask-based real estate data API.

## Overview

The PropBolt Real Estate API provides comprehensive real estate data services including property search, details, comparables, AVM, and AI-powered insights. It's built with Python Flask and deployed on Google Cloud App Engine with auto-scaling capabilities.

## Features

- **Complete Real Estate Data**: 13 comprehensive endpoints
- **Property Search & Details**: Advanced property information retrieval
- **Comparables Analysis**: Property comparison data (v2 & v3)
- **AVM (Automated Valuation Model)**: Property value estimation
- **AI-Powered PropGPT**: Intelligent property insights
- **Address Services**: Autocomplete and verification
- **Reports & Analytics**: Property liens and mapping
- **Real-time Health Monitoring**: Built-in health checks for Google Cloud
- **Auto-scaling**: Enterprise-grade scaling with load balancing

## API Endpoints

### Core Services
- `GET /` - API information and endpoint listing
- `GET /health` - Service health status
- `GET /docs` - Interactive API documentation (Swagger UI)

### Property Services
- `POST /v2/PropertySearch` - Search for properties
- `POST /v2/PropertyDetail` - Get detailed property information
- `POST /v2/PropertyDetailBulk` - Bulk property details retrieval
- `POST /v1/PropertyParcel` - Property parcel information

### Comparables & Valuation
- `POST /v2/PropertyComps` - Property comparables (v2)
- `POST /v3/PropertyComps` - Property comparables (v3)
- `POST /v2/PropertyAvm` - Automated Valuation Model

### Address Services
- `POST /v2/AutoComplete` - Address autocomplete
- `POST /v2/AddressVerification` - Address verification

### Advanced Features
- `POST /v2/PropGPT` - AI-powered property insights
- `POST /v2/CSVBuilder` - CSV data builder
- `POST /v2/Reports/PropertyLiens` - Property liens reports
- `POST /v2/PropertyMapping` - Property mapping services

## API Key Management

The API now includes a comprehensive API key management system with the following features:

### Authentication
All real estate API endpoints now require valid API keys:
- **API Key Header**: `Authorization: Bearer pb_live_your_api_key_here`
- **Alternative Header**: `X-API-Key: pb_live_your_api_key_here`
- **Query Parameter**: `?api_key=pb_live_your_api_key_here` (less secure)

### API Key Types
- **Live Keys**: `pb_live_` prefix for production use
- **Test Keys**: `pb_test_` prefix for development/testing

### Tier System
- **Basic**: 60 requests/minute, 1,000 requests/day
- **Premium**: 300 requests/minute, 10,000 requests/day
- **Enterprise**: 1,000 requests/minute, 100,000 requests/day

### Admin Endpoints
- `POST /admin/keys/generate` - Generate new API keys
- `GET /admin/keys` - List all API keys
- `DELETE /admin/keys/{key_id}` - Revoke API keys
- `GET /admin/usage/{key_id}` - Get usage analytics
- `GET /admin/tiers` - Get tier configurations

### Rate Limiting
- Per-minute rate limiting based on API key tier
- Rate limit headers included in responses:
  - `X-RateLimit-Limit`: Maximum requests per minute
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: When the rate limit resets

### Usage Analytics
- Real-time usage tracking and logging
- Daily usage summaries for efficient quota management
- Comprehensive analytics including response times and error rates

## Configuration

Environment variables are configured in `.env` and `app.yaml`:

- `PORT`: Server port (default: 8081)
- `FLASK_ENV`: Flask environment (production)
- `DATABASE_URL`: PostgreSQL connection string
- `REAL_ESTATE_API_KEY`: External API key
- `CORS_ALLOWED_ORIGINS`: Allowed CORS origins

## Database

Uses the same Google Cloud PostgreSQL database as the main API:
- Host: *************
- Database: propbolt
- SSL required

### API Key Management Tables
- `api_keys`: Store API keys with metadata and quotas
- `api_usage`: Log all API requests for analytics
- `daily_usage_summary`: Aggregate daily usage per key
- `rate_limit_windows`: Track rate limiting windows

## Deployment

The service is automatically deployed with the main deployment script:

```bash
./deploy-gcp.sh
```

Or deploy individually:

```bash
cd data
gcloud app deploy app.yaml --quiet --promote
```

## Setup and Installation

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Initialize Database
```bash
# Set environment variables first
export DB_HOST=*************
export DB_NAME=propbolt
export DB_USER=propbolt_user
export DB_PASSWORD=PropBolt2024!

# Initialize API key management tables
python init_api_keys_db.py
```

### 3. Local Development
```bash
# Set environment variables
cp .env.example .env
# Edit .env with your configuration

# Run the server
python main.py
```

The server will start on `http://localhost:8081`

### 4. Generate API Keys
```bash
# Using curl to generate an API key (requires admin token)
curl -X POST http://localhost:8081/admin/keys/generate \
  -H "Content-Type: application/json" \
  -H "X-Admin-Token: admin_token_placeholder" \
  -d '{
    "user_id": "admin-001",
    "name": "My Test Key",
    "tier": "basic",
    "key_type": "test"
  }'
```

### 5. Test API Endpoints
```bash
# Test with API key
curl -X POST http://localhost:8081/v2/PropertySearch \
  -H "Authorization: Bearer pb_test_your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{"address": "123 Main St"}'
```

## Architecture

- **Runtime**: Python 3.9
- **Framework**: Flask with CORS support
- **Database**: PostgreSQL with psycopg2
- **Deployment**: Google Cloud App Engine
- **Auto-scaling**: 1-100 instances based on CPU/throughput
- **Resources**: 2 CPU, 4GB RAM per instance

## Security

- HTTPS enforced
- CORS configured for allowed origins
- Database connections use SSL
- Google Cloud service account authentication

## Monitoring

- Health checks at `/health`
- Structured logging with configurable levels
- Google Cloud monitoring integration
