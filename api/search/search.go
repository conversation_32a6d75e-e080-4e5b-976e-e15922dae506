package search

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"time"

	"propbolt/autocomplete"
	"propbolt/utils"
)

func ForSale(
	pagination int,
	zoomValue int,
	neLat, neLong, swLat, swLong float64,
	isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo bool,
	priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax int,
	proxyURL *url.URL,
) ([]ListResult, []MapResult, error) {
	return ForSaleWithRetry(
		pagination, zoomValue, neLat, neLong, swLat, swLong,
		isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo,
		priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
		proxyURL, utils.DefaultRetryConfig(),
	)
}

// ForSaleByLocation searches for properties for sale using address, URL, or ID
func ForSaleByLocation(
	pagination int,
	zoomValue int,
	address, propertyURL string,
	propertyID int64,
	isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo bool,
	priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax int,
	proxyURL *url.URL,
) ([]ListResult, []MapResult, error) {
	return ForSaleByLocationWithRetry(
		pagination, zoomValue, address, propertyURL, propertyID,
		isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo,
		priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
		proxyURL, utils.DefaultRetryConfig(),
	)
}

func ForSaleWithRetry(
	pagination int,
	zoomValue int,
	neLat, neLong, swLat, swLong float64,
	isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo bool,
	priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax int,
	proxyURL *url.URL,
	retryConfig utils.RetryConfig,
) ([]ListResult, []MapResult, error) {
	filters := FilterInputSale{
		SortSelection:        JustValueString{Value: "globalrelevanceex"},
		IsAllHomes:           JustValueBool{Value: isAllHomes},
		IsElementarySchool:   JustValueBool{Value: isElementarySchool},
		IsMiddleSchool:       JustValueBool{Value: isMiddleSchool},
		IsHighSchool:         JustValueBool{Value: isHighSchool},
		IsPublicSchool:       JustValueBool{Value: isPublicSchool},
		IsPrivateSchool:      JustValueBool{Value: isPrivateSchool},
		IsCharterSchool:      JustValueBool{Value: isCharterSchool},
		IncludeUnratedSchools: JustValueBool{Value: includeUnratedSchools},
		IsTownhouse:          JustValueBool{Value: isTownhouse},
		IsMultiFamily:        JustValueBool{Value: isMultiFamily},
		IsCondo:              JustValueBool{Value: isCondo},
		IsLotLand:            JustValueBool{Value: isLotLand},
		IsApartment:          JustValueBool{Value: isApartment},
		IsManufactured:       JustValueBool{Value: isManufactured},
		IsApartmentOrCondo:   JustValueBool{Value: isApartmentOrCondo},
		Price:                PriceRange{Max: priceMax, Min: priceMin},
		MonthlyPayment:       PaymentRange{Max: monthlyPaymentMax, Min: monthlyPaymentMin},
	}

	result, retryResult := utils.WithRetry(func() (searchResult, error) {
		listResults, mapResults, err := search(pagination, zoomValue, neLat, neLong, swLat, swLong, filters, proxyURL)
		return searchResult{ListResults: listResults, MapResults: mapResults}, err
	}, retryConfig)

	if !retryResult.Success {
		return nil, nil, fmt.Errorf("search failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
	}

	return result.ListResults, result.MapResults, nil
}

func ForSaleByLocationWithRetry(
	pagination int,
	zoomValue int,
	address, propertyURL string,
	propertyID int64,
	isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo bool,
	priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax int,
	proxyURL *url.URL,
	retryConfig utils.RetryConfig,
) ([]ListResult, []MapResult, error) {
	// Get coordinates from address, URL, or ID
	bounds, err := getLocationBounds(address, propertyURL, propertyID, proxyURL)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get location bounds: %w", err)
	}

	// Use the coordinates for search
	return ForSaleWithRetry(
		pagination, zoomValue, bounds.North, bounds.East, bounds.South, bounds.West,
		isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo,
		priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
		proxyURL, retryConfig,
	)
}

type searchResult struct {
	ListResults []ListResult
	MapResults  []MapResult
}

type LocationBounds struct {
	North float64
	South float64
	East  float64
	West  float64
}
func ForRent(pagination int, zoomValue int, neLat, neLong, swLat, swLong float64, proxyURL *url.URL) ([]ListResult, []MapResult, error) {
	return ForRentWithRetry(pagination, zoomValue, neLat, neLong, swLat, swLong, proxyURL, utils.DefaultRetryConfig())
}

// ForRentByLocation searches for rental properties using address, URL, or ID
func ForRentByLocation(pagination int, zoomValue int, address, propertyURL string, propertyID int64, proxyURL *url.URL) ([]ListResult, []MapResult, error) {
	return ForRentByLocationWithRetry(pagination, zoomValue, address, propertyURL, propertyID, proxyURL, utils.DefaultRetryConfig())
}

func ForRentByLocationWithRetry(pagination int, zoomValue int, address, propertyURL string, propertyID int64, proxyURL *url.URL, retryConfig utils.RetryConfig) ([]ListResult, []MapResult, error) {
	// Get coordinates from address, URL, or ID
	bounds, err := getLocationBounds(address, propertyURL, propertyID, proxyURL)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get location bounds: %w", err)
	}

	// Use the coordinates for search
	return ForRentWithRetry(pagination, zoomValue, bounds.North, bounds.East, bounds.South, bounds.West, proxyURL, retryConfig)
}

func ForRentWithRetry(pagination int, zoomValue int, neLat, neLong, swLat, swLong float64, proxyURL *url.URL, retryConfig utils.RetryConfig) ([]ListResult, []MapResult, error) {
	rent := FilterInputRent{
		SortSelection: JustValueString{Value: "priorityscore"},
		IsAllHomes:    JustValueBool{Value: true},
		IsForRent:     JustValueBool{Value: true},
	}

	result, retryResult := utils.WithRetry(func() (searchResult, error) {
		listResults, mapResults, err := search(pagination, zoomValue, neLat, neLong, swLat, swLong, rent, proxyURL)
		return searchResult{ListResults: listResults, MapResults: mapResults}, err
	}, retryConfig)

	if !retryResult.Success {
		return nil, nil, fmt.Errorf("search failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
	}

	return result.ListResults, result.MapResults, nil
}

func Sold(pagination int, zoomValue int, neLat, neLong, swLat, swLong float64, proxyURL *url.URL) ([]ListResult, []MapResult, error) {
	return SoldWithRetry(pagination, zoomValue, neLat, neLong, swLat, swLong, proxyURL, utils.DefaultRetryConfig())
}

// SoldByLocation searches for sold properties using address, URL, or ID
func SoldByLocation(pagination int, zoomValue int, address, propertyURL string, propertyID int64, proxyURL *url.URL) ([]ListResult, []MapResult, error) {
	return SoldByLocationWithRetry(pagination, zoomValue, address, propertyURL, propertyID, proxyURL, utils.DefaultRetryConfig())
}

func SoldByLocationWithRetry(pagination int, zoomValue int, address, propertyURL string, propertyID int64, proxyURL *url.URL, retryConfig utils.RetryConfig) ([]ListResult, []MapResult, error) {
	// Get coordinates from address, URL, or ID
	bounds, err := getLocationBounds(address, propertyURL, propertyID, proxyURL)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get location bounds: %w", err)
	}

	// Use the coordinates for search
	return SoldWithRetry(pagination, zoomValue, bounds.North, bounds.East, bounds.South, bounds.West, proxyURL, retryConfig)
}

func SoldWithRetry(pagination int, zoomValue int, neLat, neLong, swLat, swLong float64, proxyURL *url.URL, retryConfig utils.RetryConfig) ([]ListResult, []MapResult, error) {
	sold := FilterInputSold{
		SortSelection:  JustValueString{Value: "globalrelevanceex"},
		IsAllHomes:     JustValueBool{Value: true},
		IsRecentlySold: JustValueBool{Value: true},
	}

	result, retryResult := utils.WithRetry(func() (searchResult, error) {
		listResults, mapResults, err := search(pagination, zoomValue, neLat, neLong, swLat, swLong, sold, proxyURL)
		return searchResult{ListResults: listResults, MapResults: mapResults}, err
	}, retryConfig)

	if !retryResult.Success {
		return nil, nil, fmt.Errorf("search failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
	}

	return result.ListResults, result.MapResults, nil
}

func search[filtersTypes FilterInputSale | FilterInputRent | FilterInputSold](pagination, zoomValue int, neLat, neLong, swLat, swLong float64, filterState filtersTypes, proxyURL *url.URL) ([]ListResult, []MapResult, error) {
	searchReq := SearchRequest{
		SearchQueryState: SearchQueryState{
			IsListVisible:   true,
			IsMapVisible:    true,
			MapBounds: MapBounds{
				North: neLat,
				East:  neLong,
				South: swLat,
				West:  swLong,
			},
			FilterState:     filterState,
			MapZoom:         zoomValue,
			UsersSearchTerm: "",
			Pagination:      Pagination{CurrentPage: pagination},
		},
		Wants:          Wants{Cat1: []string{"listResults", "mapResults"}, Cat2: []string{"total"}},
		RequestId:      10,
		IsDebugRequest: false,
	}
	rawData, err := json.Marshal(searchReq)
	if err != nil {
		return nil, nil, err
	}
	req, err := http.NewRequest("PUT", ep, bytes.NewReader(rawData))
	if err != nil {
		return nil, nil, err
	}
	utils.SetJSONHeaders(req)
	req.Header.Set("x-caller-id", "hops-homepage")
	req.Header.Set("Referer", "https://www.zillow.com/")
	client := utils.CreateHTTPClient(proxyURL, time.Minute)
	resp, err := client.Do(req)
	if err != nil {
		return nil, nil, err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, err
	}
	if resp.StatusCode != 200 {
		errData := fmt.Errorf("status: %d headers: %+v", resp.StatusCode, resp.Header)
		return nil, nil, errData
	}
	body = utils.RemoveSpaceByte(body)
	var output output
	if err := json.Unmarshal(body, &output); err != nil {
		return nil, nil, err
	}
	return output.Cat1.SearchResults.ListResults, output.Cat1.SearchResults.MapResults, nil
}

// getLocationBounds extracts coordinates from address, URL, or ID and returns search bounds
func getLocationBounds(address, propertyURL string, propertyID int64, proxyURL *url.URL) (LocationBounds, error) {
	var lat, lng float64
	var err error

	// Priority: ID > URL > Address
	if propertyID > 0 {
		lat, lng, err = getCoordinatesFromID(propertyID, proxyURL)
	} else if propertyURL != "" {
		lat, lng, err = getCoordinatesFromURL(propertyURL, proxyURL)
	} else if address != "" {
		lat, lng, err = getCoordinatesFromAddress(address, proxyURL)
	} else {
		return LocationBounds{}, fmt.Errorf("must provide address, propertyURL, or propertyID")
	}

	if err != nil {
		return LocationBounds{}, err
	}

	// Create bounds around the coordinates (approximately 1 mile radius)
	latOffset := 0.01 // roughly 1 mile
	lngOffset := 0.01

	return LocationBounds{
		North: lat + latOffset,
		South: lat - latOffset,
		East:  lng + lngOffset,
		West:  lng - lngOffset,
	}, nil
}

// getCoordinatesFromID extracts coordinates from a Zillow property ID
func getCoordinatesFromID(propertyID int64, proxyURL *url.URL) (float64, float64, error) {
	propertyURL := fmt.Sprintf("https://www.zillow.com/homedetails/any-title/%d_zpid/", propertyID)
	return getCoordinatesFromURL(propertyURL, proxyURL)
}

// getCoordinatesFromURL extracts coordinates from a Zillow property URL
func getCoordinatesFromURL(propertyURL string, proxyURL *url.URL) (float64, float64, error) {
	// Extract property ID from URL
	re := regexp.MustCompile(`/(\d+)_zpid/`)
	matches := re.FindStringSubmatch(propertyURL)
	if len(matches) < 2 {
		return 0, 0, fmt.Errorf("invalid Zillow URL format")
	}

	_, err := strconv.ParseInt(matches[1], 10, 64)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid property ID in URL: %w", err)
	}

	// For now, use autocomplete to get approximate coordinates
	// In a full implementation, you would fetch the property details page
	// and extract coordinates from the page data
	return getCoordinatesFromAddress("", proxyURL) // Fallback to default coordinates
}

// getCoordinatesFromAddress extracts coordinates from an address using autocomplete
func getCoordinatesFromAddress(address string, proxyURL *url.URL) (float64, float64, error) {
	if address == "" {
		// Default to a central location (e.g., Manhattan) for property-specific searches
		return 40.7589, -73.9851, nil
	}

	// Use autocomplete to get suggestions
	suggestions, err := autocomplete.GetSuggestions(address, proxyURL)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get address suggestions: %w", err)
	}

	if len(suggestions.Results) == 0 {
		return 0, 0, fmt.Errorf("no suggestions found for address: %s", address)
	}

	// Get region details for the first suggestion
	firstResult := suggestions.Results[0]
	if firstResult.RegionID == 0 {
		// Default coordinates if no region ID
		return 40.7589, -73.9851, nil
	}

	regionDetails, err := autocomplete.GetRegionDetails(firstResult.RegionID, proxyURL)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get region details: %w", err)
	}

	// Calculate center of region bounds
	bounds := regionDetails.RegionBounds
	centerLat := (bounds.North + bounds.South) / 2
	centerLng := (bounds.East + bounds.West) / 2

	return centerLat, centerLng, nil
}

// ForSaleEnhanced - Enhanced search function with land-specific filters
func ForSaleEnhanced(
	pagination int,
	zoomValue int,
	neLat, neLong, swLat, swLong float64,
	isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo bool,
	priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax int,
	// Land-specific parameters
	lotSizeMin, lotSizeMax int,
	hasUtilities, hasWater, hasSewer, hasElectric, hasGas, isWaterfront, hasView, isBuildable, hasRoadAccess bool,
	zoningType string,
	proxyURL *url.URL,
) ([]ListResult, []MapResult, error) {
	return ForSaleEnhancedWithRetry(
		pagination, zoomValue, neLat, neLong, swLat, swLong,
		isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo,
		priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
		lotSizeMin, lotSizeMax,
		hasUtilities, hasWater, hasSewer, hasElectric, hasGas, isWaterfront, hasView, isBuildable, hasRoadAccess,
		zoningType,
		proxyURL, utils.DefaultRetryConfig(),
	)
}

// ForSaleEnhancedWithRetry - Enhanced search function with retry logic and land-specific filters
func ForSaleEnhancedWithRetry(
	pagination int,
	zoomValue int,
	neLat, neLong, swLat, swLong float64,
	isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool, isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools, isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment, isManufactured, isApartmentOrCondo bool,
	priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax int,
	// Land-specific parameters
	lotSizeMin, lotSizeMax int,
	hasUtilities, hasWater, hasSewer, hasElectric, hasGas, isWaterfront, hasView, isBuildable, hasRoadAccess bool,
	zoningType string,
	proxyURL *url.URL,
	retryConfig utils.RetryConfig,
) ([]ListResult, []MapResult, error) {
	filters := FilterInputSale{
		SortSelection:        JustValueString{Value: "globalrelevanceex"},
		IsAllHomes:           JustValueBool{Value: isAllHomes},
		IsElementarySchool:   JustValueBool{Value: isElementarySchool},
		IsMiddleSchool:       JustValueBool{Value: isMiddleSchool},
		IsHighSchool:         JustValueBool{Value: isHighSchool},
		IsPublicSchool:       JustValueBool{Value: isPublicSchool},
		IsPrivateSchool:      JustValueBool{Value: isPrivateSchool},
		IsCharterSchool:      JustValueBool{Value: isCharterSchool},
		IncludeUnratedSchools: JustValueBool{Value: includeUnratedSchools},
		IsTownhouse:          JustValueBool{Value: isTownhouse},
		IsMultiFamily:        JustValueBool{Value: isMultiFamily},
		IsCondo:              JustValueBool{Value: isCondo},
		IsLotLand:            JustValueBool{Value: isLotLand},
		IsApartment:          JustValueBool{Value: isApartment},
		IsManufactured:       JustValueBool{Value: isManufactured},
		IsApartmentOrCondo:   JustValueBool{Value: isApartmentOrCondo},
		Price:                PriceRange{Max: priceMax, Min: priceMin},
		MonthlyPayment:       PaymentRange{Max: monthlyPaymentMax, Min: monthlyPaymentMin},

		// Land-specific filters
		LotSize:              LotSizeRange{Max: lotSizeMax, Min: lotSizeMin},
		HasUtilities:         JustValueBool{Value: hasUtilities},
		HasWater:             JustValueBool{Value: hasWater},
		HasSewer:             JustValueBool{Value: hasSewer},
		HasElectric:          JustValueBool{Value: hasElectric},
		HasGas:               JustValueBool{Value: hasGas},
		IsWaterfront:         JustValueBool{Value: isWaterfront},
		HasView:              JustValueBool{Value: hasView},
		ZoningType:           JustValueString{Value: zoningType},
		IsBuildable:          JustValueBool{Value: isBuildable},
		HasRoadAccess:        JustValueBool{Value: hasRoadAccess},
	}

	result, retryResult := utils.WithRetry(func() (searchResult, error) {
		listResults, mapResults, err := search(pagination, zoomValue, neLat, neLong, swLat, swLong, filters, proxyURL)
		return searchResult{ListResults: listResults, MapResults: mapResults}, err
	}, retryConfig)

	if !retryResult.Success {
		return nil, nil, fmt.Errorf("enhanced search failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
	}

	return result.ListResults, result.MapResults, nil
}
